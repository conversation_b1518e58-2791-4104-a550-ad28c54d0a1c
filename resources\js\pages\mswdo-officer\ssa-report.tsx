import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Table, Download, Filter, Calculator, FileSpreadsheet, ArrowUpDown, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Budget and DSS',
        href: '#',
    },
    {
        title: 'Social Services Assistance',
        href: '/mswdo-officer/ssa-report',
    },
];

export default function SocialServicesAssistance() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Social Services Assistance Report" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                {/* <PERSON>er */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Social Services Assistance</h1>
                        <p className="text-purple-600 mt-2">Budget allocation spreadsheet and assistance distribution tracking</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Calculator className="h-4 w-4 mr-2" />
                            Calculate
                        </Button>
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                            <Download className="h-4 w-4 mr-2" />
                            Export Excel
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Budget</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">₱5,000,000</div>
                            <p className="text-xs text-purple-600">FY 2024</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Distributed</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">₱3,250,000</div>
                            <p className="text-xs text-green-600">65% utilized</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Beneficiaries</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">1,245</div>
                            <p className="text-xs text-blue-600">Families served</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">This Month</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">₱425,000</div>
                            <p className="text-xs text-orange-600">Distributed</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Spreadsheet Controls */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Search className="h-5 w-5 mr-2" />
                            Spreadsheet Controls
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Search Rows</label>
                                <Input
                                    placeholder="Search budget items..."
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Filter by Quarter</label>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Quarters</option>
                                    <option value="q1">Q1 2024</option>
                                    <option value="q2">Q2 2024</option>
                                    <option value="q3">Q3 2024</option>
                                    <option value="q4">Q4 2024</option>
                                </select>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Service Category</label>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Categories</option>
                                    <option value="financial">Financial Assistance</option>
                                    <option value="medical">Medical Assistance</option>
                                    <option value="educational">Educational Assistance</option>
                                </select>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Sort By</label>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="budget_desc">Budget (High to Low)</option>
                                    <option value="budget_asc">Budget (Low to High)</option>
                                    <option value="utilization">Utilization Rate</option>
                                    <option value="category">Category</option>
                                </select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Social Services Assistance Report Table */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <FileSpreadsheet className="h-5 w-5 mr-2" />
                            Social Service Assistance Given as of February 2023
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Barangay-wise breakdown of social services assistance distribution
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="overflow-x-auto">
                            <table className="w-full border-collapse border border-gray-800 text-sm">
                                <thead>
                                    <tr className="bg-red-600 text-white">
                                        <th rowSpan={2} className="border border-gray-800 px-2 py-2 text-center font-bold min-w-[120px]">BARANGAY</th>
                                        <th colSpan={2} className="border border-gray-800 px-2 py-2 text-center font-bold">BURIAL</th>
                                        <th colSpan={2} className="border border-gray-800 px-2 py-2 text-center font-bold">MEDICAL</th>
                                        <th colSpan={2} className="border border-gray-800 px-2 py-2 text-center font-bold">FINANCIAL</th>
                                        <th colSpan={2} className="border border-gray-800 px-2 py-2 text-center font-bold">EDUCATIONAL</th>
                                        <th colSpan={2} className="border border-gray-800 px-2 py-2 text-center font-bold">GRAND TOTAL</th>
                                    </tr>
                                    <tr className="bg-red-600 text-white">
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">NO. OF BENEFICIARY</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">TOTAL AMOUNT</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">NO. OF BENEFICIARY</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">TOTAL AMOUNT</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">NO. OF BENEFICIARY</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">TOTAL AMOUNT</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">NO. OF BENEFICIARY</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">TOTAL AMOUNT</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">BENEFICIARY</th>
                                        <th className="border border-gray-800 px-2 py-1 text-center font-bold text-xs">AMOUNT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">WAWA</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">2</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">6,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">10</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">25,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">1</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">3,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">13</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">34,000</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">SAN JUAN</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">2</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">4,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">10</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">25,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">1</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">3,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">13</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">32,000</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">LONGOS</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">3</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">7,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">3</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">7,000</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">PANGINAY</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">3</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">7,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">19</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">45,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">22</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">52,000</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">BOROL 1ST</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">3</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">6,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">16</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">43,500</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">19</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">49,500</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">BOROL 2ND</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">2</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">4,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">6</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">16,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">8</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">20,000</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">SANTOL</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">3</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">7,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">9</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">23,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">12</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">30,000</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">PULONG GUBAT</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">1</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">2,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">8</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">20,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">9</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">22,000</td>
                                    </tr>
                                    <tr className="hover:bg-gray-50">
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">DALIG</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">1</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">2,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">2</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">4,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">*</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">3</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center font-medium">6,000</td>
                                    </tr>
                                    <tr className="bg-red-600 text-white font-bold">
                                        <td className="border border-gray-800 px-2 py-1 text-center">TOTAL</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">17</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">38,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">81</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">206,500</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">1</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">3,000</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">0</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">0</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center">99</td>
                                        <td className="border border-gray-800 px-2 py-1 text-center text-red-300">247,500</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        {/* Report Footer */}
                        <div className="flex items-center justify-between pt-4 text-sm text-gray-600">
                            <div className="flex items-center gap-4">
                                <span>Barangays: 9</span>
                                <span>•</span>
                                <span>Total Beneficiaries: 99</span>
                                <span>•</span>
                                <span>Total Amount: ₱247,500</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                    Print Report
                                </Button>
                                <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                    Export to Excel
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
