import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { CreditCard, Download, Filter, TrendingUp, Users, Calendar, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Budget and DSS',
        href: '#',
    },
    {
        title: 'Social Services Assistance',
        href: '/mswdo-officer/ssa-report',
    },
];

export default function SocialServicesAssistance() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Social Services Assistance Report" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Social Services Assistance</h1>
                        <p className="text-purple-600 mt-2">Budget allocation and assistance distribution reports</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                            <Download className="h-4 w-4 mr-2" />
                            Export Report
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Budget</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">₱5,000,000</div>
                            <p className="text-xs text-purple-600">FY 2024</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Distributed</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">₱3,250,000</div>
                            <p className="text-xs text-green-600">65% utilized</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Beneficiaries</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">1,245</div>
                            <p className="text-xs text-blue-600">Families served</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">This Month</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">₱425,000</div>
                            <p className="text-xs text-orange-600">Distributed</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Filter className="h-5 w-5 mr-2" />
                            Filter Reports
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Date Range</label>
                                <Input 
                                    type="date"
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Barangay</label>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Barangays</option>
                                    <option value="poblacion">Poblacion</option>
                                    <option value="bagong-nayon">Bagong Nayon</option>
                                </select>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Service Type</label>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Services</option>
                                    <option value="financial">Financial Assistance</option>
                                    <option value="medical">Medical Assistance</option>
                                    <option value="educational">Educational Assistance</option>
                                </select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Reports Grid */}
                <div className="grid gap-6 md:grid-cols-2">
                    <Card className="border-purple-200">
                        <CardHeader>
                            <CardTitle className="text-purple-900 flex items-center">
                                <TrendingUp className="h-5 w-5 mr-2" />
                                Budget Utilization
                            </CardTitle>
                            <CardDescription className="text-purple-600">
                                Monthly budget allocation and utilization trends
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-12 text-purple-600">
                                <TrendingUp className="h-16 w-16 mx-auto mb-4 text-purple-300" />
                                <h3 className="text-lg font-semibold mb-2">Budget Utilization Chart</h3>
                                <p className="text-sm">Interactive chart showing budget allocation vs utilization</p>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900 flex items-center">
                                <Users className="h-5 w-5 mr-2" />
                                Beneficiary Distribution
                            </CardTitle>
                            <CardDescription className="text-blue-600">
                                Assistance distribution across barangays
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-12 text-blue-600">
                                <Users className="h-16 w-16 mx-auto mb-4 text-blue-300" />
                                <h3 className="text-lg font-semibold mb-2">Distribution Map</h3>
                                <p className="text-sm">Geographic distribution of assistance by barangay</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Assistance Table */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <CreditCard className="h-5 w-5 mr-2" />
                            Recent Assistance Distribution
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Latest assistance provided to beneficiaries
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="text-center py-12 text-purple-600">
                            <CreditCard className="h-16 w-16 mx-auto mb-4 text-purple-300" />
                            <h3 className="text-lg font-semibold mb-2">Assistance Records</h3>
                            <p className="text-sm mb-4">Detailed table of assistance distribution records</p>
                            <div className="text-xs text-purple-500 space-y-1">
                                <p>• Beneficiary information</p>
                                <p>• Assistance type and amount</p>
                                <p>• Distribution date and status</p>
                                <p>• Social worker assigned</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
