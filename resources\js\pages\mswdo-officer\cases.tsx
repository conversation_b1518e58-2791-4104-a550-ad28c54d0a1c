import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import {
    Search, Plus, Filter, Download, Edit, Trash2, MoreHorizontal,
    ArrowUpDown, FileSpreadsheet, Eye, Calendar
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    { title: 'Dashboard', href: '/mswdo-officer/dashboard' },
    { title: 'Case Management', href: '/mswdo-officer/cases' },
];

// Excel-like case data structure matching their current spreadsheet
const cases = [
    {
        id: 1,
        caseloadNo: 'BSC-001',
        date: '2024-03-15',
        names: 'Maria Santos',
        birthdate: '1979-05-12',
        age: 45,
        address: 'Barangay San Jose, Balagtas',
        contactNumber: '09123456789',
        typeOfViolation: 'Medical Assistance',
        dateReported: '2024-03-15',
        counselling: 'Initial Assessment',
        counsellin: 'Maria Gonzales',
        remarks: 'Emergency medical assistance for hospitalization'
    },
    {
        id: 2,
        caseloadNo: 'BSC-002',
        date: '2024-03-14',
        names: 'Juan Dela Cruz',
        birthdate: '1989-08-23',
        age: 35,
        address: 'Barangay Poblacion, Balagtas',
        contactNumber: '09987654321',
        typeOfViolation: 'Educational Support',
        dateReported: '2024-03-14',
        counselling: 'Under Review',
        counsellin: 'Ana Reyes',
        remarks: 'Educational assistance for children school supplies'
    },
    {
        id: 3,
        caseloadNo: 'BSC-003',
        date: '2024-03-10',
        names: 'Ana Rodriguez',
        birthdate: '1996-02-18',
        age: 28,
        address: 'Barangay Longos, Balagtas',
        contactNumber: '09456789123',
        typeOfViolation: 'Financial Aid',
        dateReported: '2024-03-10',
        counselling: 'Completed',
        counsellin: 'Juan Santos',
        remarks: 'Emergency financial assistance - case resolved'
    },
    {
        id: 4,
        caseloadNo: 'BSC-004',
        date: '2024-03-12',
        names: 'Pedro Reyes',
        birthdate: '1955-11-30',
        age: 68,
        address: 'Barangay Wawa, Balagtas',
        contactNumber: '09234567890',
        typeOfViolation: 'Senior Citizen Assistance',
        dateReported: '2024-03-12',
        counselling: 'In Progress',
        counsellin: 'Maria Gonzales',
        remarks: 'Monthly pension and medical support'
    },
    {
        id: 5,
        caseloadNo: 'BSC-005',
        date: '2024-03-16',
        names: 'Carmen Villanueva',
        birthdate: '1985-07-04',
        age: 39,
        address: 'Barangay Borol 1st, Balagtas',
        contactNumber: '09345678901',
        typeOfViolation: 'Housing Assistance',
        dateReported: '2024-03-16',
        counselling: 'Pending',
        counsellin: 'Ana Reyes',
        remarks: 'Temporary shelter assistance due to fire incident'
    }
];

// Simple statistics for Excel-like interface
const stats = {
    totalCases: cases.length,
    pendingCases: cases.filter(c => c.counselling === 'Pending').length,
    inProgressCases: cases.filter(c => c.counselling === 'In Progress' || c.counselling === 'Under Review').length,
    completedCases: cases.filter(c => c.counselling === 'Completed').length
};

// Simple case types for filtering (matching Excel data)
const caseTypes = [
    'Medical Assistance',
    'Educational Support',
    'Financial Aid',
    'Senior Citizen Assistance',
    'Housing Assistance',
    'Food Assistance',
    'Transportation Aid',
    'Burial Assistance'
];

// Simple counselling statuses (matching Excel terminology)
const counsellingStatuses = [
    'Pending',
    'Initial Assessment',
    'Under Review',
    'In Progress',
    'Completed'
];

export default function CaseManagement() {
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');
    const [sortField, setSortField] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
    const [selectedCaseForMobile, setSelectedCaseForMobile] = useState<typeof cases[0] | null>(null);

    const getCounsellingBadge = (status: string) => {
        switch (status) {
            case 'Pending':
                return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
            case 'Initial Assessment':
                return <Badge variant="outline" className="bg-blue-100 text-blue-800">Initial Assessment</Badge>;
            case 'Under Review':
                return <Badge variant="outline" className="bg-purple-100 text-purple-800">Under Review</Badge>;
            case 'In Progress':
                return <Badge variant="outline" className="bg-orange-100 text-orange-800">In Progress</Badge>;
            case 'Completed':
                return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric'
        });
    };

    const calculateAge = (birthdate: string) => {
        const today = new Date();
        const birth = new Date(birthdate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        return age;
    };

    const filteredCases = cases.filter(caseItem => {
        const matchesSearch =
            caseItem.caseloadNo.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.names.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.typeOfViolation.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.remarks.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesStatus = statusFilter === 'all' || caseItem.counselling === statusFilter;
        const matchesType = typeFilter === 'all' || caseItem.typeOfViolation === typeFilter;

        return matchesSearch && matchesStatus && matchesType;
    });

    const sortedCases = [...filteredCases].sort((a, b) => {
        if (!sortField) return 0;

        const aValue = a[sortField as keyof typeof a];
        const bValue = b[sortField as keyof typeof b];

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Case Management" />

            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Case Management</h1>
                        <p className="text-purple-600 mt-2">Excel-like case management and tracking system</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="flex items-center gap-2">
                            <FileSpreadsheet className="h-4 w-4" />
                            Export to Excel
                        </Button>
                        <Button asChild className="bg-purple-600 hover:bg-purple-700">
                            <Link href="/mswdo-officer/cases/new">
                                <Plus className="h-4 w-4 mr-2" />
                                Add New Case
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Simple Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Cases</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">{stats.totalCases}</div>
                            <p className="text-sm text-purple-600">All recorded cases</p>
                        </CardContent>
                    </Card>

                    <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-yellow-900">Pending</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-yellow-900">{stats.pendingCases}</div>
                            <p className="text-sm text-yellow-600">Awaiting action</p>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">In Progress</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">{stats.inProgressCases}</div>
                            <p className="text-sm text-blue-600">Currently active</p>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Completed</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">{stats.completedCases}</div>
                            <p className="text-sm text-green-600">Successfully resolved</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Simple Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search cases..."
                            className="pl-10"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                    <div className="flex gap-2">
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="w-[140px]">
                                <SelectValue placeholder="All Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Status</SelectItem>
                                {counsellingStatuses.map(status => (
                                    <SelectItem key={status} value={status}>
                                        {status}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Select value={typeFilter} onValueChange={setTypeFilter}>
                            <SelectTrigger className="w-[160px]">
                                <SelectValue placeholder="All Types" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Types</SelectItem>
                                {caseTypes.map(type => (
                                    <SelectItem key={type} value={type}>
                                        {type}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                    <p className="text-sm text-gray-600">
                        Showing {sortedCases.length} of {stats.totalCases} cases
                    </p>
                    <div className="text-xs text-gray-500 sm:hidden">
                        Tap on any row for full details
                    </div>
                </div>

                {/* Responsive Excel-like Table */}
                <Card className="border-purple-200">
                    <CardContent className="p-0">
                        <div className="w-full">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-purple-50">
                                        {/* Always visible core columns */}
                                        <TableHead className="min-w-[90px] max-w-[110px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('caseloadNo')} className="h-auto p-0 font-semibold text-xs">
                                                Case No.
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="min-w-[120px] max-w-[180px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('names')} className="h-auto p-0 font-semibold text-xs">
                                                Names
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="min-w-[50px] max-w-[60px] font-semibold text-purple-900 text-center">Age</TableHead>

                                        {/* Hide on small screens */}
                                        <TableHead className="hidden sm:table-cell min-w-[80px] max-w-[100px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('date')} className="h-auto p-0 font-semibold text-xs">
                                                Date
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>

                                        {/* Hide on medium and smaller screens */}
                                        <TableHead className="hidden md:table-cell min-w-[150px] max-w-[200px] font-semibold text-purple-900">Address</TableHead>
                                        <TableHead className="hidden md:table-cell min-w-[100px] max-w-[120px] font-semibold text-purple-900">Contact</TableHead>

                                        {/* Hide on large and smaller screens */}
                                        <TableHead className="hidden lg:table-cell min-w-[80px] max-w-[100px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('birthdate')} className="h-auto p-0 font-semibold text-xs">
                                                Birthdate
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>

                                        {/* Hide on extra large and smaller screens */}
                                        <TableHead className="hidden xl:table-cell min-w-[120px] max-w-[150px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('typeOfViolation')} className="h-auto p-0 font-semibold text-xs">
                                                Type
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="hidden xl:table-cell min-w-[80px] max-w-[100px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('dateReported')} className="h-auto p-0 font-semibold text-xs">
                                                Reported
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>

                                        {/* Always visible status and actions */}
                                        <TableHead className="min-w-[100px] max-w-[120px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('counselling')} className="h-auto p-0 font-semibold text-xs">
                                                Status
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="hidden lg:table-cell min-w-[100px] max-w-[120px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('counsellin')} className="h-auto p-0 font-semibold text-xs">
                                                Counselor
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="hidden lg:table-cell min-w-[150px] max-w-[200px] font-semibold text-purple-900">Remarks</TableHead>
                                        <TableHead className="min-w-[80px] max-w-[100px] font-semibold text-purple-900 text-center">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {sortedCases.map((caseItem) => (
                                        <TableRow
                                            key={caseItem.id}
                                            className="hover:bg-purple-50/50 cursor-pointer lg:cursor-default"
                                            onClick={() => {
                                                // Only show modal on mobile/tablet screens
                                                if (window.innerWidth < 1024) {
                                                    setSelectedCaseForMobile(caseItem);
                                                }
                                            }}
                                        >
                                            {/* Always visible core columns */}
                                            <TableCell className="font-medium text-purple-900 text-sm">{caseItem.caseloadNo}</TableCell>
                                            <TableCell className="font-medium text-sm">
                                                <div className="truncate" title={caseItem.names}>
                                                    {caseItem.names}
                                                </div>
                                                {/* Show additional info on mobile */}
                                                <div className="sm:hidden text-xs text-gray-500 mt-1">
                                                    {formatDate(caseItem.date)} • {caseItem.typeOfViolation}
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-center text-sm">{caseItem.age}</TableCell>

                                            {/* Hide on small screens */}
                                            <TableCell className="hidden sm:table-cell text-sm">{formatDate(caseItem.date)}</TableCell>

                                            {/* Hide on medium and smaller screens */}
                                            <TableCell className="hidden md:table-cell text-sm">
                                                <div className="truncate max-w-[180px]" title={caseItem.address}>
                                                    {caseItem.address}
                                                </div>
                                            </TableCell>
                                            <TableCell className="hidden md:table-cell text-sm">
                                                <div className="truncate" title={caseItem.contactNumber}>
                                                    {caseItem.contactNumber}
                                                </div>
                                            </TableCell>

                                            {/* Hide on large and smaller screens */}
                                            <TableCell className="hidden lg:table-cell text-sm">{formatDate(caseItem.birthdate)}</TableCell>

                                            {/* Hide on extra large and smaller screens */}
                                            <TableCell className="hidden xl:table-cell text-sm">
                                                <div className="truncate" title={caseItem.typeOfViolation}>
                                                    {caseItem.typeOfViolation}
                                                </div>
                                            </TableCell>
                                            <TableCell className="hidden xl:table-cell text-sm">{formatDate(caseItem.dateReported)}</TableCell>

                                            {/* Always visible status and actions */}
                                            <TableCell className="text-sm">
                                                {getCounsellingBadge(caseItem.counselling)}
                                            </TableCell>
                                            <TableCell className="hidden lg:table-cell text-sm">
                                                <div className="truncate" title={caseItem.counsellin}>
                                                    {caseItem.counsellin}
                                                </div>
                                            </TableCell>
                                            <TableCell className="hidden lg:table-cell text-sm">
                                                <div className="truncate max-w-[180px]" title={caseItem.remarks}>
                                                    {caseItem.remarks}
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-center">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/mswdo-officer/cases/${caseItem.id}`}>
                                                                <Eye className="h-4 w-4 mr-2" />
                                                                View
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/mswdo-officer/cases/${caseItem.id}/edit`}>
                                                                <Edit className="h-4 w-4 mr-2" />
                                                                Edit
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Download className="h-4 w-4 mr-2" />
                                                            Export
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem className="text-red-600">
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        {sortedCases.length === 0 && (
                            <div className="p-12 text-center">
                                <div className="text-gray-400 mb-4">
                                    <Calendar className="h-12 w-12 mx-auto" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No cases found</h3>
                                <p className="text-gray-500 mb-4">
                                    {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
                                        ? 'Try adjusting your search criteria or filters.'
                                        : 'Get started by adding your first case.'
                                    }
                                </p>
                                <Button asChild>
                                    <Link href="/mswdo-officer/cases/new">
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add New Case
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>

                {/* Mobile Detail Modal */}
                <Dialog open={!!selectedCaseForMobile} onOpenChange={() => setSelectedCaseForMobile(null)}>
                    <DialogContent className="max-w-md">
                        <DialogHeader>
                            <DialogTitle className="text-purple-900">Case Details</DialogTitle>
                        </DialogHeader>
                        {selectedCaseForMobile && (
                            <div className="space-y-4">
                                <div className="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span className="font-medium text-gray-600">Case No:</span>
                                        <p className="text-purple-900 font-medium">{selectedCaseForMobile.caseloadNo}</p>
                                    </div>
                                    <div>
                                        <span className="font-medium text-gray-600">Date:</span>
                                        <p>{formatDate(selectedCaseForMobile.date)}</p>
                                    </div>
                                    <div className="col-span-2">
                                        <span className="font-medium text-gray-600">Name:</span>
                                        <p className="font-medium">{selectedCaseForMobile.names}</p>
                                    </div>
                                    <div>
                                        <span className="font-medium text-gray-600">Age:</span>
                                        <p>{selectedCaseForMobile.age}</p>
                                    </div>
                                    <div>
                                        <span className="font-medium text-gray-600">Birthdate:</span>
                                        <p>{formatDate(selectedCaseForMobile.birthdate)}</p>
                                    </div>
                                    <div className="col-span-2">
                                        <span className="font-medium text-gray-600">Address:</span>
                                        <p>{selectedCaseForMobile.address}</p>
                                    </div>
                                    <div className="col-span-2">
                                        <span className="font-medium text-gray-600">Contact:</span>
                                        <p>{selectedCaseForMobile.contactNumber}</p>
                                    </div>
                                    <div className="col-span-2">
                                        <span className="font-medium text-gray-600">Type:</span>
                                        <p>{selectedCaseForMobile.typeOfViolation}</p>
                                    </div>
                                    <div>
                                        <span className="font-medium text-gray-600">Reported:</span>
                                        <p>{formatDate(selectedCaseForMobile.dateReported)}</p>
                                    </div>
                                    <div>
                                        <span className="font-medium text-gray-600">Status:</span>
                                        <div className="mt-1">{getCounsellingBadge(selectedCaseForMobile.counselling)}</div>
                                    </div>
                                    <div className="col-span-2">
                                        <span className="font-medium text-gray-600">Counselor:</span>
                                        <p>{selectedCaseForMobile.counsellin}</p>
                                    </div>
                                    <div className="col-span-2">
                                        <span className="font-medium text-gray-600">Remarks:</span>
                                        <p>{selectedCaseForMobile.remarks}</p>
                                    </div>
                                </div>
                                <div className="flex gap-2 pt-4">
                                    <Button variant="outline" size="sm" asChild className="flex-1">
                                        <Link href={`/mswdo-officer/cases/${selectedCaseForMobile.id}`}>
                                            <Eye className="h-4 w-4 mr-2" />
                                            View Full
                                        </Link>
                                    </Button>
                                    <Button variant="outline" size="sm" asChild className="flex-1">
                                        <Link href={`/mswdo-officer/cases/${selectedCaseForMobile.id}/edit`}>
                                            <Edit className="h-4 w-4 mr-2" />
                                            Edit
                                        </Link>
                                    </Button>
                                </div>
                            </div>
                        )}
                    </DialogContent>
                </Dialog>
            </div>
        </AppLayout>
    );
}
