import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { TrendingUp, Calculator, BarChart, Target, Calendar, MapPin } from 'lucide-react';
import { Button } from '@/components/ui/button';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Budget and DSS',
        href: '#',
    },
    {
        title: 'Decision Support System',
        href: '/mswdo-officer/dss-report',
    },
];

export default function DecisionSupportSystem() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Decision Support System" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Decision Support System</h1>
                        <p className="text-purple-600 mt-2">Data-driven insights for budget planning and resource allocation</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Calculator className="h-4 w-4 mr-2" />
                            Calculate
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                            <Target className="h-4 w-4 mr-2" />
                            Generate Plan
                        </Button>
                    </div>
                </div>

                {/* Key Metrics */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Demand Forecast</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">+15%</div>
                            <p className="text-xs text-purple-600">Next quarter</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Efficiency Score</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">87%</div>
                            <p className="text-xs text-blue-600">Resource utilization</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Impact Rating</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">4.2/5</div>
                            <p className="text-xs text-green-600">Program effectiveness</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Risk Level</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">Low</div>
                            <p className="text-xs text-orange-600">Budget variance</p>
                        </CardContent>
                    </Card>
                </div>

                {/* DSS Tools Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="border-purple-200">
                        <CardHeader>
                            <CardTitle className="text-purple-900 flex items-center">
                                <Calendar className="h-5 w-5 mr-2" />
                                Annual Planning
                            </CardTitle>
                            <CardDescription className="text-purple-600">
                                Strategic budget planning for the fiscal year
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="text-center py-8">
                                    <Calendar className="h-12 w-12 mx-auto mb-3 text-purple-300" />
                                    <h4 className="font-medium text-purple-900 mb-2">FY 2025 Planning</h4>
                                    <p className="text-sm text-purple-600">Budget allocation recommendations</p>
                                </div>
                                <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                                    Open Planner
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200">
                        <CardHeader>
                            <CardTitle className="text-blue-900 flex items-center">
                                <MapPin className="h-5 w-5 mr-2" />
                                Barangay Allocation
                            </CardTitle>
                            <CardDescription className="text-blue-600">
                                Optimize resource distribution across barangays
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="text-center py-8">
                                    <MapPin className="h-12 w-12 mx-auto mb-3 text-blue-300" />
                                    <h4 className="font-medium text-blue-900 mb-2">28 Barangays</h4>
                                    <p className="text-sm text-blue-600">Allocation optimization</p>
                                </div>
                                <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                                    Optimize Allocation
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200">
                        <CardHeader>
                            <CardTitle className="text-green-900 flex items-center">
                                <TrendingUp className="h-5 w-5 mr-2" />
                                Trend Analysis
                            </CardTitle>
                            <CardDescription className="text-green-600">
                                Historical data analysis and forecasting
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="text-center py-8">
                                    <TrendingUp className="h-12 w-12 mx-auto mb-3 text-green-300" />
                                    <h4 className="font-medium text-green-900 mb-2">3-Year Trends</h4>
                                    <p className="text-sm text-green-600">Predictive analytics</p>
                                </div>
                                <Button className="w-full bg-green-600 hover:bg-green-700 text-white">
                                    View Trends
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Analytics Dashboard */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <BarChart className="h-5 w-5 mr-2" />
                            Decision Analytics Dashboard
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Comprehensive analytics for informed decision making
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="text-center py-16 text-purple-600">
                            <BarChart className="h-20 w-20 mx-auto mb-4 text-purple-300" />
                            <h3 className="text-xl font-semibold mb-2">Interactive Analytics Dashboard</h3>
                            <p className="text-sm mb-6">Real-time data visualization and decision support tools</p>
                            <div className="text-xs text-purple-500 space-y-2 max-w-2xl mx-auto">
                                <p className="font-medium">Features to implement:</p>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-left">
                                    <p>• Budget allocation optimization algorithms</p>
                                    <p>• Demand forecasting models</p>
                                    <p>• Resource efficiency analysis</p>
                                    <p>• Impact assessment metrics</p>
                                    <p>• Risk analysis and mitigation</p>
                                    <p>• Performance benchmarking</p>
                                    <p>• Scenario planning tools</p>
                                    <p>• Automated reporting systems</p>
                                </div>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Recommendations */}
                <Card className="border-green-200">
                    <CardHeader>
                        <CardTitle className="text-green-900 flex items-center">
                            <Target className="h-5 w-5 mr-2" />
                            AI-Powered Recommendations
                        </CardTitle>
                        <CardDescription className="text-green-600">
                            Smart recommendations based on data analysis
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                                <h4 className="font-medium text-green-900 mb-2">Budget Reallocation Suggestion</h4>
                                <p className="text-sm text-green-700">Consider increasing medical assistance budget by 12% based on recent demand trends.</p>
                            </div>
                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <h4 className="font-medium text-blue-900 mb-2">Resource Optimization</h4>
                                <p className="text-sm text-blue-700">Barangay Poblacion shows 23% higher efficiency in program delivery. Consider expanding successful practices.</p>
                            </div>
                            <div className="p-4 bg-orange-50 border border-orange-200 rounded-lg">
                                <h4 className="font-medium text-orange-900 mb-2">Risk Alert</h4>
                                <p className="text-sm text-orange-700">Educational assistance budget may exceed allocation by Q3. Consider early intervention strategies.</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
