import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { Users, Search, Filter, CheckCircle, Clock, AlertTriangle, FileText, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'BFACES Management',
        href: '/mswdo-officer/bfaces',
    },
];

export default function BfacesManagement() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="BFACES Management" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                {/* <PERSON>er */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">BFACES Management</h1>
                        <p className="text-purple-600 mt-2">Manage verified families with completed BFACES applications and active control codes</p>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Active BFACES</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">1,456</div>
                            <p className="text-xs text-purple-600">Verified families</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Control Codes Issued</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">1,456</div>
                            <p className="text-xs text-green-600">Active codes</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Family Members</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">5,824</div>
                            <p className="text-xs text-blue-600">Total registered</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">This Month</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">89</div>
                            <p className="text-xs text-orange-600">New BFACES</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Search className="h-5 w-5 mr-2" />
                            Search BFACES Records
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="md:col-span-2">
                                <Input 
                                    placeholder="Search by head of family name, control code, or barangay..." 
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Barangays</option>
                                    <option value="poblacion">Poblacion</option>
                                    <option value="bagong-nayon">Bagong Nayon</option>
                                    <option value="san-jose">San Jose</option>
                                </select>
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="suspended">Suspended</option>
                                    <option value="expired">Expired</option>
                                </select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* BFACES Records List */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <FileText className="h-5 w-5 mr-2" />
                            BFACES Records
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Families with completed BFACES applications and active control codes
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Sample BFACES Records */}
                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                            <Users className="h-6 w-6 text-green-600" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-purple-900">Santos Family</h4>
                                            <p className="text-sm text-gray-600">Head: Maria Santos</p>
                                            <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                                                <span className="flex items-center gap-1">
                                                    <Shield className="h-3 w-3" />
                                                    BFC000123456
                                                </span>
                                                <span>Poblacion</span>
                                                <span>5 family members</span>
                                                <span>Verified: Dec 15, 2024</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge className="bg-green-100 text-green-800 border-green-200">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Active
                                        </Badge>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View BFACES
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                            <Users className="h-6 w-6 text-green-600" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-purple-900">Dela Cruz Family</h4>
                                            <p className="text-sm text-gray-600">Head: Juan Dela Cruz</p>
                                            <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                                                <span className="flex items-center gap-1">
                                                    <Shield className="h-3 w-3" />
                                                    BFC000123457
                                                </span>
                                                <span>Bagong Nayon</span>
                                                <span>4 family members</span>
                                                <span>Verified: Dec 12, 2024</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge className="bg-green-100 text-green-800 border-green-200">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Active
                                        </Badge>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View BFACES
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-4">
                                        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                                            <Users className="h-6 w-6 text-orange-600" />
                                        </div>
                                        <div>
                                            <h4 className="font-semibold text-purple-900">Rodriguez Family</h4>
                                            <p className="text-sm text-gray-600">Head: Ana Rodriguez</p>
                                            <div className="flex items-center gap-4 text-xs text-gray-500 mt-1">
                                                <span className="flex items-center gap-1">
                                                    <Shield className="h-3 w-3" />
                                                    BFC000123458
                                                </span>
                                                <span>San Jose</span>
                                                <span>3 family members</span>
                                                <span>Verified: Dec 10, 2024</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-3">
                                        <Badge className="bg-orange-100 text-orange-800 border-orange-200">
                                            <Clock className="h-3 w-3 mr-1" />
                                            Review Required
                                        </Badge>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View BFACES
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between pt-6 border-t border-purple-100">
                            <p className="text-sm text-gray-600">Showing 1-3 of 1,456 BFACES records</p>
                            <div className="flex gap-2">
                                <Button size="sm" variant="outline" disabled>Previous</Button>
                                <Button size="sm" variant="outline">Next</Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
