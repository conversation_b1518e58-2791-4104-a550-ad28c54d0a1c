<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\BfacesForm;
use App\Models\BfacesDocument;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class BfacesController extends Controller
{
    /**
     * Generate a new BFACES Control Code for approved Head of Family
     */
    public function generateControlCode(string $userId): RedirectResponse
    {
        $user = User::findOrFail($userId);

        // Verify user is Head of Family and documents are approved
        if ($user->family_role !== 'head_of_family' ||
            $user->document_verification_status !== 'approved') {
            return back()->withErrors([
                'message' => 'Control code can only be generated for approved Heads of Family.'
            ]);
        }

        // Check if user already has a control code
        if ($user->bfaces_control_code) {
            return back()->withErrors([
                'message' => 'This family already has a BFACES Control Code.'
            ]);
        }

        // Generate unique control code
        $controlCode = $user->generateBfacesControlCode();

        // Update BFACES status
        $user->update([
            'bfaces_status' => 'form_accessible'
        ]);

        return back()->with('success',
            "BFACES Control Code {$controlCode} generated successfully for {$user->name}."
        );
    }

    /**
     * Approve document verification
     */
    public function approveDocuments(string $userId): RedirectResponse
    {
        $user = User::findOrFail($userId);

        $user->update([
            'document_verification_status' => 'approved',
            'documents_verified_at' => now(),
            'verified_by' => Auth::id(),
            'bfaces_status' => $user->family_role === 'head_of_family' ? 'form_accessible' : 'approved'
        ]);

        // Auto-generate control code for Head of Family
        if ($user->family_role === 'head_of_family' && !$user->bfaces_control_code) {
            $user->generateBfacesControlCode();
        }

        return back()->with('success',
            "Documents approved for {$user->name}. " .
            ($user->family_role === 'head_of_family' ? 'BFACES form is now accessible.' : 'Family member linked successfully.')
        );
    }

    /**
     * Reject document verification
     */
    public function rejectDocuments(Request $request, string $userId): RedirectResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500'
        ]);

        $user = User::findOrFail($userId);

        $user->update([
            'document_verification_status' => 'rejected',
            'verified_by' => Auth::id(),
            'verification_notes' => $request->reason,
            'bfaces_status' => 'documents_pending'
        ]);

        return back()->with('success',
            "Documents rejected for {$user->name}. User will be notified to resubmit."
        );
    }

    /**
     * Get family members by BFACES Control Code
     */
    public function getFamilyMembers(string $controlCode): Response
    {
        $headOfFamily = User::where('bfaces_control_code', $controlCode)->first();

        if (!$headOfFamily) {
            abort(404, 'BFACES Control Code not found');
        }

        $familyMembers = $headOfFamily->familyMembers()
            ->with('profile')
            ->get();

        return Inertia::render('mswdo-officer/bfaces/family-details', [
            'headOfFamily' => $headOfFamily->load('profile'),
            'familyMembers' => $familyMembers,
            'bfacesForm' => $headOfFamily->bfacesForm
        ]);
    }
}
