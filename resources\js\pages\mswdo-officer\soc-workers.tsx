import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { UserCheck, Plus, Search, Filter, Mail, Phone, MapPin, Calendar, Users, BarChart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Administration',
        href: '#',
    },
    {
        title: 'Social Workers',
        href: '/mswdo-officer/soc-workers',
    },
];

export default function SocialWorkers() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Social Workers Management" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Social Workers Management</h1>
                        <p className="text-purple-600 mt-2">Manage social worker accounts and assignments</p>
                    </div>
                    <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                        <Plus className="h-4 w-4 mr-2" />
                        Add Social Worker
                    </Button>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Social Workers</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">12</div>
                            <p className="text-xs text-purple-600">Active staff</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Active Cases</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">156</div>
                            <p className="text-xs text-green-600">Currently assigned</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Avg. Caseload</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">13</div>
                            <p className="text-xs text-blue-600">Cases per worker</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">This Month</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">89</div>
                            <p className="text-xs text-orange-600">Cases resolved</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Search className="h-5 w-5 mr-2" />
                            Search Social Workers
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col sm:flex-row gap-4">
                            <div className="flex-1">
                                <Input 
                                    placeholder="Search by name, email, or assigned barangay..." 
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Social Workers Grid */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {/* Sample Social Worker Cards */}
                    <Card className="border-purple-200 hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                                    <UserCheck className="h-6 w-6 text-purple-600" />
                                </div>
                                <div>
                                    <CardTitle className="text-purple-900">Maria Santos</CardTitle>
                                    <CardDescription className="text-purple-600">Senior Social Worker</CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Mail className="h-4 w-4" />
                                <span><EMAIL></span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Phone className="h-4 w-4" />
                                <span>+63 ************</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <MapPin className="h-4 w-4" />
                                <span>Poblacion, Bagong Nayon</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Users className="h-4 w-4" />
                                <span>15 active cases</span>
                            </div>
                            <div className="flex items-center justify-between pt-2">
                                <Badge variant="secondary" className="bg-green-100 text-green-800">Active</Badge>
                                <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                    View Details
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-purple-200 hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                    <UserCheck className="h-6 w-6 text-blue-600" />
                                </div>
                                <div>
                                    <CardTitle className="text-purple-900">Juan Dela Cruz</CardTitle>
                                    <CardDescription className="text-purple-600">Social Worker</CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Mail className="h-4 w-4" />
                                <span><EMAIL></span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Phone className="h-4 w-4" />
                                <span>+63 ************</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <MapPin className="h-4 w-4" />
                                <span>San Jose, Longos</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Users className="h-4 w-4" />
                                <span>12 active cases</span>
                            </div>
                            <div className="flex items-center justify-between pt-2">
                                <Badge variant="secondary" className="bg-green-100 text-green-800">Active</Badge>
                                <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                    View Details
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="border-purple-200 hover:shadow-lg transition-shadow">
                        <CardHeader>
                            <div className="flex items-center gap-3">
                                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                    <UserCheck className="h-6 w-6 text-green-600" />
                                </div>
                                <div>
                                    <CardTitle className="text-purple-900">Ana Rodriguez</CardTitle>
                                    <CardDescription className="text-purple-600">Social Worker</CardDescription>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Mail className="h-4 w-4" />
                                <span><EMAIL></span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Phone className="h-4 w-4" />
                                <span>+63 ************</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <MapPin className="h-4 w-4" />
                                <span>Wawa, Borol 1st</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                <Users className="h-4 w-4" />
                                <span>11 active cases</span>
                            </div>
                            <div className="flex items-center justify-between pt-2">
                                <Badge variant="secondary" className="bg-green-100 text-green-800">Active</Badge>
                                <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                    View Details
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Performance Overview */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <BarChart className="h-5 w-5 mr-2" />
                            Performance Overview
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Social worker performance metrics and workload distribution
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="text-center py-12 text-purple-600">
                            <BarChart className="h-16 w-16 mx-auto mb-4 text-purple-300" />
                            <h3 className="text-lg font-semibold mb-2">Performance Dashboard</h3>
                            <p className="text-sm mb-4">Comprehensive performance tracking and workload management</p>
                            <div className="text-xs text-purple-500 space-y-1 max-w-md mx-auto">
                                <p>• Case resolution rates</p>
                                <p>• Workload distribution</p>
                                <p>• Performance metrics</p>
                                <p>• Training and development tracking</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
