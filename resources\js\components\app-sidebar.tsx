import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type NavGroup } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    BookOpen,
    ClipboardCheck,
    FileCheck,
    FileText,
    LayoutGrid,
    ShieldCheck,
    UserCheck,
    Users,
    BarChart,
    CalendarDays,
    CheckCheck,
    Settings,
    CreditCard,
    Box,
    TrendingUp,
    MapPin,
    CheckCircle,
    Database,
    Activity
} from 'lucide-react';
import AppLogo from './app-logo';

interface PageProps {
    auth?: {
        user?: {
            role: string;
        };
    };
}

// Applicant navigation items
const applicantNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/applicant/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Residency Verification',
        href: '/applicant/residency-verification',
        icon: UserCheck,
    },
    {
        title: 'BFACES Application',
        href: '/applicant/bfaces-application',
        icon: FileText,
    },
    {
        title: 'Services',
        href: '/applicant/services',
        icon: ClipboardCheck,
    },
    {
        title: 'Applications',
        href: '/applicant/applications',
        icon: FileCheck,
    },
    {
        title: 'Appointments',
        href: '/applicant/appointments',
        icon: CalendarDays,
    },
    {
        title: 'Documents',
        href: '/applicant/documents',
        icon: BookOpen,
    },
];

// Social Worker navigation items
const socialWorkerNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/social-worker/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Verifications',
        href: '/social-worker/verifications',
        icon: CheckCheck,
    },
    {
        title: 'Applications',
        href: '/social-worker/applications',
        icon: FileCheck,
    },
    {
        title: 'Interviews',
        href: '/social-worker/interviews',
        icon: CalendarDays,
    },
    {
        title: 'Clients',
        href: '/social-worker/clients',
        icon: Users,
    },
    {
        title: 'Reports',
        href: '/social-worker/reports',
        icon: BarChart,
    },
];

// MSWDO Officer navigation groups
const mswdoOfficerNavGroups: NavGroup[] = [
    {
        title: 'Overview',
        items: [
            {
                title: 'Dashboard',
                href: '/mswdo-officer/dashboard',
                icon: LayoutGrid,
            },
        ]
    },
    {
        title: 'BFACES Management',
        items: [
            {
                title: 'Family Registry',
                href: '/mswdo-officer/bfaces',
                icon: Users,
            },
            {
                title: 'Head of Family Accounts',
                href: '/mswdo-officer/bfaces/heads',
                icon: UserCheck,
            },
            {
                title: 'Document Verification',
                href: '/mswdo-officer/bfaces/verification',
                icon: CheckCircle,
            },
            {
                title: 'BFACES Forms',
                href: '/mswdo-officer/bfaces/forms',
                icon: FileText,
            },
            {
                title: 'Control Codes',
                href: '/mswdo-officer/bfaces/codes',
                icon: ShieldCheck,
            },
        ]
    },
    {
        title: 'Case & Service Management',
        items: [
            {
                title: 'Case Management',
                href: '/mswdo-officer/cases',
                icon: FileText,
            },
            {
                title: 'Service Programs',
                href: '/mswdo-officer/services',
                icon: Box,
            },
            {
                title: 'Application Processing',
                href: '/mswdo-officer/applications',
                icon: ClipboardCheck,
            },
        ]
    },
    {
        title: 'Budget & Planning',
        items: [
            {
                title: 'Budget Allocation & DSS',
                href: '/mswdo-officer/budget',
                icon: CreditCard,
            },
            {
                title: 'Financial Planning',
                href: '/mswdo-officer/planning',
                icon: TrendingUp,
            },
            {
                title: 'Barangay Allocation',
                href: '/mswdo-officer/barangay-budget',
                icon: MapPin,
            },
        ]
    },
    {
        title: 'Analytics & Reports',
        items: [
            {
                title: 'Reports & Analytics',
                href: '/mswdo-officer/reports',
                icon: BarChart,
            },
            {
                title: 'Performance Metrics',
                href: '/mswdo-officer/metrics',
                icon: Activity,
            },
            {
                title: 'Data Export',
                href: '/mswdo-officer/export',
                icon: Database,
            },
        ]
    },
    {
        title: 'Administration',
        items: [
            {
                title: 'Staff Management',
                href: '/mswdo-officer/staff',
                icon: UserCheck,
            },
            {
                title: 'System Settings',
                href: '/mswdo-officer/settings',
                icon: Settings,
            },
            {
                title: 'Audit Logs',
                href: '/mswdo-officer/audit',
                icon: FileCheck,
            },
        ]
    },
];

const footerNavItems: NavItem[] = [
    
];

function normalizeRole(role: string): string {
    if (!role) return 'applicant';

    const roleMap: Record<string, string> = {
        'superadmin': 'mswdo-officer',
        'admin': 'mswdo-officer',
        'mswdo-officer': 'mswdo-officer',
        'MSWDO Officer': 'mswdo-officer',
        'social-worker': 'social-worker',
        'Social Worker': 'social-worker',
        'applicant': 'applicant',
        'client': 'applicant',
        'Applicant': 'applicant',
    };

    return roleMap[role.trim()] || 'applicant';
}

export function AppSidebar() {
    const { auth } = usePage().props as PageProps;
    const userRole = auth?.user?.role || 'applicant';
    const normalizedRole = normalizeRole(userRole);

    const getNavItems = () => {
        switch (normalizedRole) {
            case 'social-worker':
                return { items: socialWorkerNavItems };
            case 'mswdo-officer':
                return { groups: mswdoOfficerNavGroups };
            default:
                return { items: applicantNavItems };
        }
    };

    const getDashboardRoute = () => {
        switch (normalizedRole) {
            case 'mswdo-officer':
                return '/mswdo-officer/dashboard';
            case 'social-worker':
                return '/social-worker/dashboard';
            default:
                return '/applicant/dashboard';
        }
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={getDashboardRoute()} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain {...getNavItems()} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
