import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar';
import { type NavItem, type NavGroup } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import {
    BookOpen,
    ClipboardCheck,
    FileCheck,
    FileText,
    LayoutGrid,
    UserCheck,
    Users,
    BarChart,
    CalendarDays,
    CheckCheck,
    CreditCard,
    Box,
    TrendingUp,
    CheckCircle
} from 'lucide-react';
import AppLogo from './app-logo';

interface PageProps {
    auth?: {
        user?: {
            role: string;
        };
    };
}

// Applicant navigation items
const applicantNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/applicant/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Residency Verification',
        href: '/applicant/residency-verification',
        icon: UserCheck,
    },
    {
        title: 'BFACES Application',
        href: '/applicant/bfaces-application',
        icon: FileText,
    },
    {
        title: 'Services',
        href: '/applicant/services',
        icon: ClipboardCheck,
    },
    {
        title: 'Applications',
        href: '/applicant/applications',
        icon: FileCheck,
    },
    {
        title: 'Appointments',
        href: '/applicant/appointments',
        icon: CalendarDays,
    },
    {
        title: 'Documents',
        href: '/applicant/documents',
        icon: BookOpen,
    },
];

// Social Worker navigation items
const socialWorkerNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/social-worker/dashboard',
        icon: LayoutGrid,
    },
    {
        title: 'Verifications',
        href: '/social-worker/verifications',
        icon: CheckCheck,
    },
    {
        title: 'Applications',
        href: '/social-worker/applications',
        icon: FileCheck,
    },
    {
        title: 'Interviews',
        href: '/social-worker/interviews',
        icon: CalendarDays,
    },
    {
        title: 'Clients',
        href: '/social-worker/clients',
        icon: Users,
    },
    {
        title: 'Reports',
        href: '/social-worker/reports',
        icon: BarChart,
    },
];

// MSWDO Officer navigation groups
const mswdoOfficerNavGroups: NavGroup[] = [
    {
        title: 'Overview',
        items: [
            {
                title: 'Dashboard',
                href: '/mswdo-officer/dashboard',
                icon: LayoutGrid,
            },
        ]
    },
    {
        title: 'BFACES Management',
        items: [
            {
                title: 'BFACES Management',
                href: '/mswdo-officer/bfaces',
                icon: Users,
            },
        ]
    },
    {
        title: 'Case and Service Management',
        items: [
            {
                title: 'Case Management',
                href: '/mswdo-officer/cases',
                icon: FileText,
            },
            {
                title: 'Service Programs',
                href: '/mswdo-officer/services',
                icon: Box,
            },
            {
                title: 'Service Application Management',
                href: '/mswdo-officer/applications',
                icon: ClipboardCheck,
            },
        ]
    },
    {
        title: 'Budget and DSS',
        items: [
            {
                title: 'Social Services Assistance',
                href: '/mswdo-officer/ssa-report',
                icon: CreditCard,
            },
            {
                title: 'Decision Support System',
                href: '/mswdo-officer/dss-report',
                icon: TrendingUp,
            },
        ]
    },
    {
        title: 'Administration',
        items: [
            {
                title: 'Reports and Analytics',
                href: '/mswdo-officer/analytics',
                icon: BarChart,
            },
            {
                title: 'Social Workers',
                href: '/mswdo-officer/soc-workers',
                icon: UserCheck,
            },
            {
                title: 'Registered Applicants/Clients',
                href: '/mswdo-officer/clients',
                icon: Users,
            },
            {
                title: 'Announcements',
                href: '/mswdo-officer/announcements',
                icon: CheckCircle,
            },
        ]
    },
];

const footerNavItems: NavItem[] = [
    
];

function normalizeRole(role: string): string {
    if (!role) return 'applicant';

    const roleMap: Record<string, string> = {
        'superadmin': 'mswdo-officer',
        'admin': 'mswdo-officer',
        'mswdo-officer': 'mswdo-officer',
        'MSWDO Officer': 'mswdo-officer',
        'social-worker': 'social-worker',
        'Social Worker': 'social-worker',
        'applicant': 'applicant',
        'client': 'applicant',
        'Applicant': 'applicant',
    };

    return roleMap[role.trim()] || 'applicant';
}

export function AppSidebar() {
    const { auth } = usePage().props as PageProps;
    const userRole = auth?.user?.role || 'applicant';
    const normalizedRole = normalizeRole(userRole);

    const getNavItems = () => {
        switch (normalizedRole) {
            case 'social-worker':
                return { items: socialWorkerNavItems };
            case 'mswdo-officer':
                return { groups: mswdoOfficerNavGroups };
            default:
                return { items: applicantNavItems };
        }
    };

    const getDashboardRoute = () => {
        switch (normalizedRole) {
            case 'mswdo-officer':
                return '/mswdo-officer/dashboard';
            case 'social-worker':
                return '/social-worker/dashboard';
            default:
                return '/applicant/dashboard';
        }
    };

    return (
        <Sidebar collapsible="icon" variant="inset">
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton size="lg" asChild>
                            <Link href={getDashboardRoute()} prefetch>
                                <AppLogo />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent>
                <NavMain {...getNavItems()} />
            </SidebarContent>

            <SidebarFooter>
                <NavFooter items={footerNavItems} className="mt-auto" />
                <NavUser />
            </SidebarFooter>
        </Sidebar>
    );
}
