import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, TrendingUp, Download, Filter, Calendar, MapPin, Users, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Administration',
        href: '#',
    },
    {
        title: 'Reports and Analytics',
        href: '/mswdo-officer/analytics',
    },
];

export default function ReportsAndAnalytics() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Reports and Analytics" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Reports and Analytics</h1>
                        <p className="text-purple-600 mt-2">Comprehensive reporting and data analytics dashboard</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Filter className="h-4 w-4 mr-2" />
                            Filter
                        </Button>
                        <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                            <Download className="h-4 w-4 mr-2" />
                            Export All
                        </Button>
                    </div>
                </div>

                {/* Key Performance Indicators */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Beneficiaries</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">2,847</div>
                            <p className="text-xs text-purple-600">+12% from last month</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Services Provided</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">1,245</div>
                            <p className="text-xs text-blue-600">This month</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Budget Utilization</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">78%</div>
                            <p className="text-xs text-green-600">Of allocated budget</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Active Cases</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">156</div>
                            <p className="text-xs text-orange-600">Under review</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Report Categories */}
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    <Card className="border-purple-200 hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader>
                            <CardTitle className="text-purple-900 flex items-center">
                                <MapPin className="h-5 w-5 mr-2" />
                                Services Per Barangay
                            </CardTitle>
                            <CardDescription className="text-purple-600">
                                Geographic distribution of services and assistance
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-6">
                                <MapPin className="h-12 w-12 mx-auto mb-3 text-purple-300" />
                                <p className="text-sm text-purple-600 mb-4">Interactive map with service distribution</p>
                                <Button size="sm" className="bg-purple-600 hover:bg-purple-700 text-white">
                                    View Report
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader>
                            <CardTitle className="text-blue-900 flex items-center">
                                <CreditCard className="h-5 w-5 mr-2" />
                                ID Applications
                            </CardTitle>
                            <CardDescription className="text-blue-600">
                                Senior, PWD, Solo Parent ID application analytics
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-6">
                                <CreditCard className="h-12 w-12 mx-auto mb-3 text-blue-300" />
                                <p className="text-sm text-blue-600 mb-4">ID application trends and statistics</p>
                                <Button size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
                                    View Report
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader>
                            <CardTitle className="text-green-900 flex items-center">
                                <BarChart className="h-5 w-5 mr-2" />
                                Certificates Issued
                            </CardTitle>
                            <CardDescription className="text-green-600">
                                Certificate types and issuance statistics
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-6">
                                <BarChart className="h-12 w-12 mx-auto mb-3 text-green-300" />
                                <p className="text-sm text-green-600 mb-4">Certificate issuance analytics</p>
                                <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                                    View Report
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader>
                            <CardTitle className="text-orange-900 flex items-center">
                                <TrendingUp className="h-5 w-5 mr-2" />
                                Performance Trends
                            </CardTitle>
                            <CardDescription className="text-orange-600">
                                Historical performance and trend analysis
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-6">
                                <TrendingUp className="h-12 w-12 mx-auto mb-3 text-orange-300" />
                                <p className="text-sm text-orange-600 mb-4">Multi-year performance trends</p>
                                <Button size="sm" className="bg-orange-600 hover:bg-orange-700 text-white">
                                    View Report
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-indigo-200 hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader>
                            <CardTitle className="text-indigo-900 flex items-center">
                                <Users className="h-5 w-5 mr-2" />
                                Beneficiary Demographics
                            </CardTitle>
                            <CardDescription className="text-indigo-600">
                                Demographic analysis of service recipients
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-6">
                                <Users className="h-12 w-12 mx-auto mb-3 text-indigo-300" />
                                <p className="text-sm text-indigo-600 mb-4">Age, gender, and family composition</p>
                                <Button size="sm" className="bg-indigo-600 hover:bg-indigo-700 text-white">
                                    View Report
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-pink-200 hover:shadow-lg transition-shadow cursor-pointer">
                        <CardHeader>
                            <CardTitle className="text-pink-900 flex items-center">
                                <PieChart className="h-5 w-5 mr-2" />
                                Budget Analysis
                            </CardTitle>
                            <CardDescription className="text-pink-600">
                                Budget allocation and utilization analysis
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="text-center py-6">
                                <PieChart className="h-12 w-12 mx-auto mb-3 text-pink-300" />
                                <p className="text-sm text-pink-600 mb-4">Financial performance metrics</p>
                                <Button size="sm" className="bg-pink-600 hover:bg-pink-700 text-white">
                                    View Report
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Filters */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Filter className="h-5 w-5 mr-2" />
                            Quick Report Filters
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Generate custom reports with specific criteria
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Date Range</label>
                                <Input 
                                    type="date"
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Barangay</label>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Barangays</option>
                                    <option value="poblacion">Poblacion</option>
                                    <option value="bagong-nayon">Bagong Nayon</option>
                                </select>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-purple-900 mb-2 block">Service Type</label>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Services</option>
                                    <option value="financial">Financial Assistance</option>
                                    <option value="medical">Medical Assistance</option>
                                </select>
                            </div>
                            <div className="flex items-end">
                                <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white">
                                    Generate Report
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
