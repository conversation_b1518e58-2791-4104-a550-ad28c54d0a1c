import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { ClipboardCheck, Search, Filter, Calendar, User, CheckCircle, Clock, XCircle, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Case and Service Management',
        href: '#',
    },
    {
        title: 'Service Application Management',
        href: '/mswdo-officer/applications',
    },
];

export default function ServiceApplicationManagement() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Service Application Management" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Service Application Management</h1>
                        <p className="text-purple-600 mt-2">Manage and process service applications from registered families</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Filter className="h-4 w-4 mr-2" />
                            Export Report
                        </Button>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Applications</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">1,245</div>
                            <p className="text-xs text-purple-600">All time</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Pending Review</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">89</div>
                            <p className="text-xs text-orange-600">Awaiting decision</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Approved</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">987</div>
                            <p className="text-xs text-green-600">This month: 156</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-red-200 bg-gradient-to-br from-red-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-red-900">Denied</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-900">169</div>
                            <p className="text-xs text-red-600">With feedback</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Search className="h-5 w-5 mr-2" />
                            Search Applications
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                            <div className="md:col-span-2">
                                <Input 
                                    placeholder="Search by applicant name, application ID, or service type..." 
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="approved">Approved</option>
                                    <option value="denied">Denied</option>
                                </select>
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Services</option>
                                    <option value="financial">Financial Assistance</option>
                                    <option value="medical">Medical Assistance</option>
                                    <option value="educational">Educational Assistance</option>
                                </select>
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Barangays</option>
                                    <option value="poblacion">Poblacion</option>
                                    <option value="bagong-nayon">Bagong Nayon</option>
                                </select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Applications List */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <ClipboardCheck className="h-5 w-5 mr-2" />
                            Service Applications
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Review and process applications from registered families
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Sample Application Entries */}
                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h4 className="font-semibold text-purple-900">Financial Assistance - Medical Emergency</h4>
                                            <Badge className="bg-orange-100 text-orange-800 border-orange-200">
                                                <Clock className="h-3 w-3 mr-1" />
                                                Pending
                                            </Badge>
                                            <Badge variant="outline" className="border-red-200 text-red-700">High Priority</Badge>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                                            <div>
                                                <span className="font-medium">Applicant:</span> Maria Santos (Head of Family)
                                            </div>
                                            <div>
                                                <span className="font-medium">For:</span> Juan Santos (Son, 8 years old)
                                            </div>
                                            <div>
                                                <span className="font-medium">Amount:</span> ₱15,000
                                            </div>
                                            <div>
                                                <span className="font-medium">Barangay:</span> Poblacion
                                            </div>
                                            <div>
                                                <span className="font-medium">Applied:</span> Dec 15, 2024
                                            </div>
                                            <div>
                                                <span className="font-medium">BFACES:</span> BFC000123456
                                            </div>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Emergency medical assistance for child's hospitalization due to pneumonia. 
                                            Supporting documents: Medical certificate, hospital bills, income statement.
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2 ml-4">
                                        <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                                            <CheckCircle className="h-3 w-3 mr-1" />
                                            Approve
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-red-200 text-red-700">
                                            <XCircle className="h-3 w-3 mr-1" />
                                            Deny
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View Details
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h4 className="font-semibold text-purple-900">Educational Assistance - School Supplies</h4>
                                            <Badge className="bg-green-100 text-green-800 border-green-200">
                                                <CheckCircle className="h-3 w-3 mr-1" />
                                                Approved
                                            </Badge>
                                            <Badge variant="outline" className="border-blue-200 text-blue-700">Regular</Badge>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                                            <div>
                                                <span className="font-medium">Applicant:</span> Juan Dela Cruz (Head of Family)
                                            </div>
                                            <div>
                                                <span className="font-medium">For:</span> Ana Dela Cruz (Daughter, 12 years old)
                                            </div>
                                            <div>
                                                <span className="font-medium">Amount:</span> ₱3,500
                                            </div>
                                            <div>
                                                <span className="font-medium">Barangay:</span> Bagong Nayon
                                            </div>
                                            <div>
                                                <span className="font-medium">Approved:</span> Dec 12, 2024
                                            </div>
                                            <div>
                                                <span className="font-medium">Social Worker:</span> Maria Santos
                                            </div>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Educational assistance for school supplies and uniform for Grade 6 student. 
                                            Application approved and funds disbursed.
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2 ml-4">
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View Details
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-blue-200 text-blue-700">
                                            Print Receipt
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h4 className="font-semibold text-purple-900">Medical Assistance - Senior Citizen</h4>
                                            <Badge className="bg-red-100 text-red-800 border-red-200">
                                                <XCircle className="h-3 w-3 mr-1" />
                                                Denied
                                            </Badge>
                                            <Badge variant="outline" className="border-orange-200 text-orange-700">Under Appeal</Badge>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 mb-3">
                                            <div>
                                                <span className="font-medium">Applicant:</span> Ana Rodriguez (Head of Family)
                                            </div>
                                            <div>
                                                <span className="font-medium">For:</span> Pedro Rodriguez (Father, 72 years old)
                                            </div>
                                            <div>
                                                <span className="font-medium">Amount:</span> ₱8,000
                                            </div>
                                            <div>
                                                <span className="font-medium">Barangay:</span> San Jose
                                            </div>
                                            <div>
                                                <span className="font-medium">Denied:</span> Dec 10, 2024
                                            </div>
                                            <div>
                                                <span className="font-medium">Reason:</span> Incomplete documents
                                            </div>
                                        </div>
                                        <p className="text-sm text-gray-600">
                                            Medical assistance for senior citizen's medication. Denied due to missing income certification. 
                                            Applicant has submitted appeal with additional documents.
                                        </p>
                                    </div>
                                    <div className="flex items-center gap-2 ml-4">
                                        <Button size="sm" variant="outline" className="border-orange-200 text-orange-700">
                                            <AlertTriangle className="h-3 w-3 mr-1" />
                                            Review Appeal
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            View Details
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between pt-6 border-t border-purple-100">
                            <p className="text-sm text-gray-600">Showing 1-3 of 1,245 applications</p>
                            <div className="flex gap-2">
                                <Button size="sm" variant="outline" disabled>Previous</Button>
                                <Button size="sm" variant="outline">Next</Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
