import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { CheckCircle, Plus, Search, Filter, Calendar, Users, Eye, Edit, Trash2, Megaphone } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'Administration',
        href: '#',
    },
    {
        title: 'Announcements',
        href: '/mswdo-officer/announcements',
    },
];

export default function Announcements() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Announcements Management" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">Announcements Management</h1>
                        <p className="text-purple-600 mt-2">Create and manage public announcements and notifications</p>
                    </div>
                    <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Announcement
                    </Button>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Announcements</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">24</div>
                            <p className="text-xs text-purple-600">All time</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Active</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">8</div>
                            <p className="text-xs text-green-600">Currently published</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Total Views</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">12,456</div>
                            <p className="text-xs text-blue-600">This month</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Scheduled</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">3</div>
                            <p className="text-xs text-orange-600">Future posts</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Search className="h-5 w-5 mr-2" />
                            Search Announcements
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="md:col-span-2">
                                <Input 
                                    placeholder="Search by title, content, or category..." 
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Status</option>
                                    <option value="published">Published</option>
                                    <option value="draft">Draft</option>
                                    <option value="scheduled">Scheduled</option>
                                </select>
                            </div>
                            <div>
                                <select className="w-full h-10 px-3 border border-purple-200 rounded-md focus:border-purple-400">
                                    <option value="">All Categories</option>
                                    <option value="general">General</option>
                                    <option value="services">Services</option>
                                    <option value="events">Events</option>
                                </select>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Announcements List */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Megaphone className="h-5 w-5 mr-2" />
                            Announcements
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Manage public announcements and notifications
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {/* Sample Announcement Entries */}
                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h4 className="font-semibold text-purple-900">New Medical Assistance Program Available</h4>
                                            <Badge className="bg-green-100 text-green-800 border-green-200">Published</Badge>
                                            <Badge variant="outline" className="border-blue-200 text-blue-700">Services</Badge>
                                        </div>
                                        <p className="text-sm text-gray-600 mb-3">
                                            We are pleased to announce the launch of our new medical assistance program for senior citizens and PWDs. 
                                            This program provides financial support for medical expenses...
                                        </p>
                                        <div className="flex items-center gap-4 text-xs text-gray-500">
                                            <span className="flex items-center gap-1">
                                                <Calendar className="h-3 w-3" />
                                                Published: Dec 15, 2024
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <Eye className="h-3 w-3" />
                                                1,234 views
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <Users className="h-3 w-3" />
                                                Target: All residents
                                            </span>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2 ml-4">
                                        <Button size="sm" variant="outline" className="border-blue-200 text-blue-700">
                                            <Eye className="h-3 w-3 mr-1" />
                                            View
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            <Edit className="h-3 w-3 mr-1" />
                                            Edit
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-red-200 text-red-700">
                                            <Trash2 className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h4 className="font-semibold text-purple-900">BFACES Registration Drive - January 2025</h4>
                                            <Badge className="bg-orange-100 text-orange-800 border-orange-200">Scheduled</Badge>
                                            <Badge variant="outline" className="border-green-200 text-green-700">Events</Badge>
                                        </div>
                                        <p className="text-sm text-gray-600 mb-3">
                                            Join our BFACES registration drive happening in all barangays this January. 
                                            Bring your required documents for faster processing...
                                        </p>
                                        <div className="flex items-center gap-4 text-xs text-gray-500">
                                            <span className="flex items-center gap-1">
                                                <Calendar className="h-3 w-3" />
                                                Scheduled: Jan 1, 2025
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <Eye className="h-3 w-3" />
                                                0 views
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <Users className="h-3 w-3" />
                                                Target: Unregistered families
                                            </span>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2 ml-4">
                                        <Button size="sm" variant="outline" className="border-blue-200 text-blue-700">
                                            <Eye className="h-3 w-3 mr-1" />
                                            Preview
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            <Edit className="h-3 w-3 mr-1" />
                                            Edit
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-red-200 text-red-700">
                                            <Trash2 className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </div>
                            </div>

                            <div className="border border-purple-100 rounded-lg p-4 hover:bg-purple-50 transition-colors">
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center gap-3 mb-2">
                                            <h4 className="font-semibold text-purple-900">Holiday Office Hours Update</h4>
                                            <Badge className="bg-gray-100 text-gray-800 border-gray-200">Draft</Badge>
                                            <Badge variant="outline" className="border-purple-200 text-purple-700">General</Badge>
                                        </div>
                                        <p className="text-sm text-gray-600 mb-3">
                                            Please be informed of our updated office hours during the holiday season. 
                                            The MSWDO office will be operating on modified schedules...
                                        </p>
                                        <div className="flex items-center gap-4 text-xs text-gray-500">
                                            <span className="flex items-center gap-1">
                                                <Calendar className="h-3 w-3" />
                                                Created: Dec 10, 2024
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <Eye className="h-3 w-3" />
                                                Not published
                                            </span>
                                            <span className="flex items-center gap-1">
                                                <Users className="h-3 w-3" />
                                                Target: All residents
                                            </span>
                                        </div>
                                    </div>
                                    <div className="flex items-center gap-2 ml-4">
                                        <Button size="sm" variant="outline" className="border-blue-200 text-blue-700">
                                            <Eye className="h-3 w-3 mr-1" />
                                            Preview
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-purple-200 text-purple-700">
                                            <Edit className="h-3 w-3 mr-1" />
                                            Edit
                                        </Button>
                                        <Button size="sm" variant="outline" className="border-red-200 text-red-700">
                                            <Trash2 className="h-3 w-3" />
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Pagination */}
                        <div className="flex items-center justify-between pt-6 border-t border-purple-100">
                            <p className="text-sm text-gray-600">Showing 1-3 of 24 announcements</p>
                            <div className="flex gap-2">
                                <Button size="sm" variant="outline" disabled>Previous</Button>
                                <Button size="sm" variant="outline">Next</Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
