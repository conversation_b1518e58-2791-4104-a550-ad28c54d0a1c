import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head } from '@inertiajs/react';
import { FileText, Users, CheckCircle, Clock, Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Home',
        href: '/mswdo-officer/dashboard',
    },
    {
        title: 'BFACES Management',
        href: '/mswdo-officer/bfaces',
    },
    {
        title: 'BFACES Forms',
        href: '/mswdo-officer/bfaces/forms',
    },
];

export default function BfacesFormsIndex() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="BFACES Forms Management" />
            
            <div className="space-y-6">
                {/* Header */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <div>
                        <h1 className="text-3xl font-bold text-purple-900">BFACES Forms Management</h1>
                        <p className="text-purple-600 mt-2">Manage family registry forms and household compositions</p>
                    </div>
                </div>

                {/* Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Forms</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-purple-900">156</div>
                            <p className="text-xs text-purple-600">Submitted forms</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Pending Review</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-blue-900">23</div>
                            <p className="text-xs text-blue-600">Awaiting assessment</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Approved</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-900">128</div>
                            <p className="text-xs text-green-600">With control codes</p>
                        </CardContent>
                    </Card>
                    
                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Family Members</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-900">487</div>
                            <p className="text-xs text-orange-600">Total registered</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <Search className="h-5 w-5 mr-2" />
                            Search & Filter Forms
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-col sm:flex-row gap-4">
                            <div className="flex-1">
                                <Input 
                                    placeholder="Search by control code, head of family name, or barangay..." 
                                    className="border-purple-200 focus:border-purple-400"
                                />
                            </div>
                            <Button variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                                <Filter className="h-4 w-4 mr-2" />
                                Filter
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Forms Table */}
                <Card className="border-purple-200">
                    <CardHeader>
                        <CardTitle className="text-purple-900 flex items-center">
                            <FileText className="h-5 w-5 mr-2" />
                            BFACES Forms
                        </CardTitle>
                        <CardDescription className="text-purple-600">
                            Family registry forms with household composition data
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="text-center py-12 text-purple-600">
                            <FileText className="h-16 w-16 mx-auto mb-4 text-purple-300" />
                            <h3 className="text-lg font-semibold mb-2">BFACES Forms Management</h3>
                            <p className="text-sm mb-4">View and manage family registry forms with household compositions.</p>
                            <div className="text-xs text-purple-500 space-y-1 max-w-md mx-auto">
                                <p><strong>New Architecture:</strong></p>
                                <p>• Only Head of Family accounts exist</p>
                                <p>• Family members stored as JSON data in forms</p>
                                <p>• All assistance applications through Head of Family</p>
                                <p>• Simplified user management</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
